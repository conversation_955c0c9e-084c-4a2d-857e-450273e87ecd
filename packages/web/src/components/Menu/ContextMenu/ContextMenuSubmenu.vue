<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onUnmounted, provide, inject } from 'vue'
import { getBuildCurrentClassPrefix } from '@utils'

import type { ContextMenuSubmenuProps, ContextMenuSubmenuEmits } from './interface'

const props = withDefaults(defineProps<ContextMenuSubmenuProps>(), {
  disabled: false,
})

const emit = defineEmits<ContextMenuSubmenuEmits>()

const buildItemClassPrefix = getBuildCurrentClassPrefix('context-menu', 'item')
const buildSubmenuClassPrefix = getBuildCurrentClassPrefix('context-menu', 'submenu')
const buildMenuClassPrefix = getBuildCurrentClassPrefix('context-menu', 'menu')

const triggerRef = ref<HTMLElement>()
const submenuRef = ref<HTMLElement>()
const visible = ref(false)
const submenuStyle = reactive({
  left: '0px',
  top: '0px',
})

// 从父组件注入事件处理器
const parentHandleSelect = inject('handleSelect', null) as any

let showTimer: number | null = null
let hideTimer: number | null = null
let isMouseInTrigger = false
let isMouseInSubmenu = false

const clearTimers = () => {
  if (showTimer) {
    clearTimeout(showTimer)
    showTimer = null
  }
  if (hideTimer) {
    clearTimeout(hideTimer)
    hideTimer = null
  }
}

const showSubmenu = () => {
  if (props.disabled || !triggerRef.value) return

  clearTimers()

  // 默认在右侧显示，相对于触发器定位
  submenuStyle.left = '100%'
  submenuStyle.top = '0px'

  visible.value = true

  // 下一帧调整位置
  nextTick(() => {
    adjustSubmenuPosition()
  })
}

const hideSubmenu = () => {
  // 只有当鼠标既不在触发器也不在子菜单中时才隐藏
  if (!isMouseInTrigger && !isMouseInSubmenu) {
    clearTimers()
    hideTimer = setTimeout(() => {
      if (!isMouseInTrigger && !isMouseInSubmenu) {
        visible.value = false
      }
    }, 100) // 减少延迟时间
  }
}

const adjustSubmenuPosition = () => {
  if (!submenuRef.value || !triggerRef.value) return

  const submenu = submenuRef.value
  const trigger = triggerRef.value
  const submenuRect = submenu.getBoundingClientRect()
  const triggerRect = trigger.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 计算默认右侧位置
  const rightPosition = triggerRect.right + 4
  const leftPosition = triggerRect.left - submenuRect.width - 4

  // 检查右侧是否有足够空间，如果没有则显示在左侧
  if (rightPosition + submenuRect.width > windowWidth && leftPosition > 4) {
    submenuStyle.left = '-' + (submenuRect.width + 4) + 'px'
  } else {
    submenuStyle.left = '100%' // 保持在右侧
  }

  // 检查垂直位置，确保不超出屏幕边界
  if (triggerRect.top + submenuRect.height > windowHeight) {
    const offset = Math.min(0, windowHeight - triggerRect.top - submenuRect.height - 4)
    submenuStyle.top = offset + 'px'
  } else if (triggerRect.top < 4) {
    submenuStyle.top = 4 - triggerRect.top + 'px'
  } else {
    submenuStyle.top = '0px'
  }
}

const handleMouseEnter = () => {
  if (props.disabled) return

  isMouseInTrigger = true
  clearTimers()

  showTimer = setTimeout(() => {
    showSubmenu()
  }, 150)
}

const handleMouseLeave = () => {
  isMouseInTrigger = false
  hideSubmenu()
}

const handleSubmenuMouseEnter = () => {
  isMouseInSubmenu = true
  clearTimers()
}

const handleSubmenuMouseLeave = () => {
  isMouseInSubmenu = false
  hideSubmenu()
}

// 处理子菜单项的选择事件
const handleSelect = (value: any, event: Event) => {
  // 先关闭当前子菜单
  visible.value = false

  // 向上传递事件
  emit('select', value, event)

  // 如果有父级处理器，也调用它
  if (parentHandleSelect) {
    parentHandleSelect(value, event)
  }
}

// 处理自定义事件
const handleSubmenuSelect = (event: CustomEvent) => {
  const { value, originalEvent } = event.detail
  handleSelect(value, originalEvent)
}

// 处理触发器右键事件
const handleContextMenu = (event: Event) => {
  // 完全阻止右键事件传播，不做任何操作
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
}

// 处理子菜单右键事件
const handleSubmenuContextMenu = (event: Event) => {
  // 完全阻止右键事件传播，不做任何操作
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
}

// 处理触发器点击事件
const handleClick = (event: Event) => {
  if (props.disabled) return

  // 阻止事件冒泡，防止关闭整个菜单
  event.stopPropagation()
  event.preventDefault()

  // 清除所有定时器
  clearTimers()

  // 切换子菜单显示状态
  if (visible.value) {
    visible.value = false
  } else {
    // 直接设置菜单位置并显示
    if (!triggerRef.value) return

    // 默认在右侧显示，相对于触发器定位
    submenuStyle.left = '100%'
    submenuStyle.top = '0px'

    visible.value = true

    // 下一帧调整位置
    nextTick(() => {
      adjustSubmenuPosition()
    })
  }
}

// 提供给子组件的上下文
provide('handleSelect', handleSelect)
provide('closeMenu', () => {
  visible.value = false
})

// 监听全局点击事件来关闭子菜单
const handleGlobalClick = (event: MouseEvent) => {
  if (!visible.value) return

  const target = event.target as HTMLElement
  const submenu = submenuRef.value
  const trigger = triggerRef.value

  // 完全忽略右键相关的所有事件
  if (event.button === 2 || event.which === 3) return

  // 检查是否点击在子菜单或触发器内
  const isClickInsideSubmenu = submenu && submenu.contains(target)
  const isClickInsideTrigger = trigger && trigger.contains(target)

  if (!isClickInsideSubmenu && !isClickInsideTrigger) {
    visible.value = false
  }
}

// 监听键盘ESC事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    visible.value = false
  }
}

// 全局右键事件处理函数 - 完全阻止所有右键相关行为
const handleGlobalContextMenu = (event: MouseEvent) => {
  if (!visible.value) return

  // 无条件阻止所有右键事件的默认行为和传播
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()

  // 永远不关闭菜单
}

onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('mousedown', handleGlobalClick) // 添加mousedown监听
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('contextmenu', handleGlobalContextMenu)
})

onUnmounted(() => {
  clearTimers()
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('mousedown', handleGlobalClick) // 移除mousedown监听
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('contextmenu', handleGlobalContextMenu)
})
</script>
<template>
  <div
    ref="triggerRef"
    :class="[
      buildItemClassPrefix(),
      buildSubmenuClassPrefix(),
      {
        [buildItemClassPrefix('disabled')]: disabled,
        [buildSubmenuClassPrefix('active')]: visible,
      },
    ]"
    @contextmenu.prevent.stop="handleContextMenu"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="handleClick"
  >
    <span v-if="icon" :class="[buildItemClassPrefix('icon'), icon]">
      {{ icon }}
    </span>
    <span :class="buildItemClassPrefix('label')">{{ label }}</span>
    <span :class="buildSubmenuClassPrefix('arrow')">▶</span>

    <!-- 子菜单 -->
    <div
      v-if="visible"
      ref="submenuRef"
      :class="[buildMenuClassPrefix(), buildSubmenuClassPrefix('popup')]"
      :style="submenuStyle"
      @contextmenu.prevent.stop="handleSubmenuContextMenu"
      @mouseenter="handleSubmenuMouseEnter"
      @mouseleave="handleSubmenuMouseLeave"
      @context-menu-select="handleSubmenuSelect"
    >
      <slot />
    </div>
  </div>
</template>
