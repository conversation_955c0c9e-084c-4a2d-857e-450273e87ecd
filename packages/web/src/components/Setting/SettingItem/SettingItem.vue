<script setup lang="ts">
import { computed, useAttrs } from 'vue'
import { getClassPrefix } from "@utils";

const props = withDefaults(defineProps<{
  title?: string;
  desc?: string;
  click?: boolean;
  vip?: boolean;
  size?: 'default' | 'small';
  background?: string;
}>(), {
  size: 'default',
  background: 'var(--u-bg-color-3)'
});

const attrs = useAttrs();
// 检测是否绑定了 click 事件
const hasClick = computed(() => {
  return 'onClick' in attrs || 'onClickCapture' in attrs
});

const settingItemClassList = computed(() => {
  const classList: string[] = [];
  classList.push(getClassPrefix('setting-item'))
  if (hasClick.value) {
    classList.push('cursor-pointer');
  }
  if (props.size !== 'default') {
    classList.push(getClassPrefix('setting-item', props.size));
  }
  return classList;
})
</script>
<template>
  <div :class="[ ...settingItemClassList ]"
       :style="{ background: background }"
       v-on="{
          ...(hasClick && { click: $attrs.onClick || $attrs.onClickCapture })
        }">
    <div class="flex justify-between items-center">
      <div>
        <div class="u-fx u-gap5 u-fac" :class="getClassPrefix('setting-item-title')">
          <slot name="title">
            {{ title }}
          </slot>
        </div>
        <slot name="desc">
          <div :class="getClassPrefix('setting-item-desc')">{{ desc }}</div>
        </slot>
      </div>
      <div>
        <slot name="default"></slot>
      </div>
    </div>
    <div>
      <slot name="extra"></slot>
    </div>
  </div>
</template>
