import type { JSX } from "vue/jsx-runtime";

export interface BackTopProps {
  /**
   * @zh 显示回到顶部按钮的触发滚动高度
   * @en Display the trigger scroll height of the back to top button
   */
  visibleHeight?: number;
  /**
   * @zh 滚动事件的监听容器
   * @en Scroll event listener container
   */
  targetContainer?: string | HTMLElement;
  /**
   * @zh 滚动动画的缓动方式，可选值参考 [BTween](https://github.com/PengJiyuan/b-tween)
   * @en Easing mode of scrolling animation, refer to [BTween](https://github.com/PengJiyuan/b-tween) for optional values
   */
  easing?: string;
  /**
   * @zh 滚动动画的持续时间
   * @en Duration of scroll animation
   */
  duration?: number;
  /**
   * @zh 按钮的位置
   * @en Button position
   */
  position?: {
    right?: number;
    bottom?: number;
  }
  /**
   * @zh 按钮的大小
   * @en Button size
   */
  size?: 'small' | 'default';
}

export interface BackTopSlots {
  /**
   * @zh 自定义回到顶部按钮内容
   * @en Custom back to top button content
   */
  default?: () => JSX.Element;
}

export interface BackTopEmits {
  /**
   * @zh 点击回到顶部按钮时触发
   * @en Triggered when clicking the back to top button
   */
  (e: 'click'): void;
}
