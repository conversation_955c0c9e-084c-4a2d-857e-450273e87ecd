<script setup lang="ts">
import WebBaseLayout from './WebBaseLayout.vue'
import { LeftMenu } from '../../menu'
import type { WebLayoutSlot } from "./interface";
import { getClassPrefix } from "@utils";

// 定义插槽类型，提供智能提示
defineSlots<WebLayoutSlot>()

</script>

<template>
  <WebBaseLayout>
    <template #header>
      <t-head-menu value="item1" height="60px">
        <template v-if="$slots.logo" #logo>
          <slot name="logo"></slot>
        </template>
        <template v-if="$slots.operations" #operations>
          <slot name="operations"></slot>
        </template>
      </t-head-menu>
    </template>
    <template #left>
      <slot name="left">
        <LeftMenu />
      </slot>
    </template>
    <slot name="default">
      <div :class="[getClassPrefix('main-content-inner')]">
        <router-view />
      </div>
    </slot>
  </WebBaseLayout>
</template>

<style scoped lang="less">

</style>
