<script setup lang="ts">
import { inject } from 'vue'
import { getBuildCurrentClassPrefix } from '@utils'

import type { ContextMenuItemProps, ContextMenuItemEmits } from './interface'

const props = withDefaults(defineProps<ContextMenuItemProps>(), {
  disabled: false,
  danger: false,
})

const emit = defineEmits<ContextMenuItemEmits>()

const buildClassPrefix = getBuildCurrentClassPrefix('context-menu', 'item')

// 尝试从父组件注入方法
const parentHandleSelect = inject('handleSelect', null) as any

const handleClick = (event: Event) => {
  if (props.disabled) return

  event.stopPropagation()

  // 发送组件事件
  emit('click', props.value, event)

  // 如果有父级处理器，调用它
  if (parentHandleSelect) {
    parentHandleSelect(props.value, event)
  } else {
    // 如果没有通过inject找到处理器，创建自定义事件向上传递
    const customEvent = new CustomEvent('context-menu-select', {
      detail: { value: props.value, originalEvent: event },
      bubbles: true,
    })
    ;(event.target as HTMLElement)?.dispatchEvent(customEvent)
  }
}

const handleContextMenu = (event: Event) => {
  // 完全阻止右键事件传播，不做任何操作
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()
}
</script>
<template>
  <div
    :class="[
      buildClassPrefix(),
      {
        [buildClassPrefix('disabled')]: disabled,
        [buildClassPrefix('danger')]: danger,
      },
    ]"
    @click="handleClick"
    @contextmenu.prevent.stop="handleContextMenu"
  >
    <span v-if="icon" :class="[buildClassPrefix('icon'), icon]">
      {{ icon }}
    </span>
    <span :class="buildClassPrefix('label')">{{ label }}</span>
  </div>
</template>
