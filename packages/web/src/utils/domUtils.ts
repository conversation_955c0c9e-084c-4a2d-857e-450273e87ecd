/**
 * 添加事件监听器
 * @param element 目标元素
 * @param event 事件名称
 * @param handler 事件处理函数
 */
export function on(element: HTMLElement | Window, event: string, handler: EventListener | ((ev: any) => void)): void {
  element.addEventListener(event, handler as EventListener);
}

/**
 * 移除事件监听器
 * @param element 目标元素
 * @param event 事件名称
 * @param handler 事件处理函数
 */
export function off(element: HTMLElement | Window, event: string, handler: EventListener | ((ev: any) => void)): void {
  element.removeEventListener(event, handler as EventListener);
}
