{"name": "@xiaou66/u-web-ui", "version": "0.0.55", "description": "web-ui", "main": "./dist/index.es.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "publishConfig": {"access": "public"}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite", "prepare": "npm run build", "publish": "npm publish"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@iconify/utils": "^2.3.0", "@types/node": "^22.15.32", "@unocss/preset-icons": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue/tsconfig": "^0.7.0", "@vueuse/core": "^13.5.0", "less": "^4.4.0", "tdesign-vue-next": "^1.15.1", "typescript": "~5.8.0", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-dts": "^4.5.4", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-tsc": "^2.2.10"}, "peerDependencies": {"tdesign-vue-next": "^1.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "default": "./dist/index.es.js"}, "./dist/u-web-ui.css": "./dist/u-web-ui.css", "./package.json": "./package.json"}}