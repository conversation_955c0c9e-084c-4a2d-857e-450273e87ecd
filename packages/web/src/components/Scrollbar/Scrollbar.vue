<script setup lang="ts">
import {
  computed,
  onMounted,
  onUnmounted,
  ref,
} from 'vue';
import type {
  CSSProperties,
} from 'vue';
import Thumb from './thumb.vue';
import { getClassPrefix, isObject } from '@utils';
import type { ThumbData, ScrollbarComponentProps, ScrollbarComponentEmits, ScrollbarInstance } from './interface'

const THUMB_MIN_SIZE = 20;
const TRACK_SIZE = 15;
const props = withDefaults(defineProps<ScrollbarComponentProps>(), {
  type: 'embed',
  hide: false,
  disableHorizontal: false,
  disableVertical: false,
});

const emit = defineEmits<ScrollbarComponentEmits>();

const prefixCls = getClassPrefix('scrollbar');

const containerRef = ref<HTMLElement>();
const horizontalData = ref<ThumbData>();
const verticalData = ref<ThumbData>();
const horizontalThumbRef = ref();
const verticalThumbRef = ref();
const _hasHorizontalScrollbar = ref(false);
const _hasVerticalScrollbar = ref(false);
const hasHorizontalScrollbar = computed(
  () => _hasHorizontalScrollbar.value && !props.disableHorizontal
);
const hasVerticalScrollbar = computed(
  () => _hasVerticalScrollbar.value && !props.disableVertical
);
const isBoth = ref(false);

let resizeObserver: ResizeObserver | null = null;

const getContainerSize = () => {
  if (containerRef.value) {
    const {
      clientWidth,
      clientHeight,
      offsetWidth,
      offsetHeight,
      scrollWidth,
      scrollHeight,
      scrollTop,
      scrollLeft,
    } = containerRef.value;
    _hasHorizontalScrollbar.value = scrollWidth > clientWidth;
    _hasVerticalScrollbar.value = scrollHeight > clientHeight;
    isBoth.value =
      hasHorizontalScrollbar.value && hasVerticalScrollbar.value;
    const horizontalTrackWidth =
      props.type === 'embed' && isBoth.value
        ? offsetWidth - TRACK_SIZE
        : offsetWidth;
    const verticalTrackHeight =
      props.type === 'embed' && isBoth.value
        ? offsetHeight - TRACK_SIZE
        : offsetHeight;

    const horizontalThumbWidth = Math.round(
      horizontalTrackWidth /
      Math.min(
        scrollWidth / clientWidth,
        horizontalTrackWidth / THUMB_MIN_SIZE
      )
    );
    const maxHorizontalOffset = horizontalTrackWidth - horizontalThumbWidth;
    const horizontalRatio =
      (scrollWidth - clientWidth) / maxHorizontalOffset;
    const verticalThumbHeight = Math.round(
      verticalTrackHeight /
      Math.min(
        scrollHeight / clientHeight,
        verticalTrackHeight / THUMB_MIN_SIZE
      )
    );
    const maxVerticalOffset = verticalTrackHeight - verticalThumbHeight;
    const verticalRatio = (scrollHeight - clientHeight) / maxVerticalOffset;

    horizontalData.value = {
      ratio: horizontalRatio,
      thumbSize: horizontalThumbWidth,
      max: maxHorizontalOffset,
    };
    verticalData.value = {
      ratio: verticalRatio,
      thumbSize: verticalThumbHeight,
      max: maxVerticalOffset,
    };
    if (scrollTop > 0) {
      const verticalOffset = Math.round(
        scrollTop / (verticalData.value?.ratio ?? 1)
      );
      verticalThumbRef.value?.setOffset(verticalOffset);
    }
    if (scrollLeft > 0) {
      const horizontalOffset = Math.round(
        scrollLeft / (horizontalData.value?.ratio ?? 1)
      );
      horizontalThumbRef.value?.setOffset(horizontalOffset);
    }
  }
};

const handleResize = () => {
  getContainerSize();
};

onMounted(() => {
  getContainerSize();

  // Setup ResizeObserver for container and content
  if (containerRef.value) {
    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(containerRef.value);

    // Also observe the first child if it exists (content)
    const firstChild = containerRef.value.firstElementChild;
    if (firstChild) {
      resizeObserver.observe(firstChild as Element);
    }
  }
});

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

const handleScroll = (ev: Event) => {
  if (containerRef.value) {
    if (hasHorizontalScrollbar.value && !props.disableHorizontal) {
      const horizontalOffset = Math.round(
        containerRef.value.scrollLeft / (horizontalData.value?.ratio ?? 1)
      );
      horizontalThumbRef.value?.setOffset(horizontalOffset);
    }
    if (hasVerticalScrollbar.value && !props.disableVertical) {
      const verticalOffset = Math.round(
        containerRef.value.scrollTop / (verticalData.value?.ratio ?? 1)
      );
      verticalThumbRef.value?.setOffset(verticalOffset);
    }
  }
  emit('scroll', ev);
};

const handleHorizontalScroll = (offset: number) => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      left: offset * (horizontalData.value?.ratio ?? 1),
    });
  }
};

const handleVerticalScroll = (offset: number) => {
  if (containerRef.value) {
    containerRef.value.scrollTo({
      top: offset * (verticalData.value?.ratio ?? 1),
    });
  }
};

const style = computed(() => {
  const style: CSSProperties = {};
  if (props.type === 'track') {
    if (hasHorizontalScrollbar.value) {
      style.paddingBottom = `${TRACK_SIZE}px`;
    }
    if (hasVerticalScrollbar.value) {
      style.paddingRight = `${TRACK_SIZE}px`;
    }
  }
  return [style, props.outerStyle];
});

const cls = computed(() => [
  `${prefixCls}`,
  `${prefixCls}-type-${props.type}`,
  {
    [`${prefixCls}-both`]: isBoth.value,
  },
  props.outerClass,
]);

/**
 * @zh 滚动
 * @en scrollTo
 * @public
 * @param {number | {left?: number;top?: number}} options
 * @param {number} y
 */
const scrollTo = (options?:| number | { left?: number; top?: number; }, y?: number) => {
  if (isObject(options)) {
    containerRef.value?.scrollTo(options);
  } else if (options || y) {
    containerRef.value?.scrollTo(options as number, y as number);
  }
};

/**
 * @zh 纵向滚动
 * @en scroll vertically
 * @public
 * @param {number} top
 */
const scrollTop = (top: number) => {
  containerRef.value?.scrollTo({
    top,
  });
};

/**
 * @zh 横向滚动
 * @en scroll horizontal
 * @public
 * @param {number} left
 */
const scrollLeft = (left: number) => {
  containerRef.value?.scrollTo({
    left,
  });
};

// 暴露公共方法
defineExpose<ScrollbarInstance>({
  scrollTo,
  scrollTop,
  scrollLeft,
});
</script>
<template>
  <div :class="cls" :style="style">
    <div
      ref="containerRef"
      :class="`${prefixCls}-container`"
      v-bind="$attrs"
      @scroll="handleScroll"
    >
      <slot />
    </div>
    <thumb
      v-if="!hide && hasHorizontalScrollbar"
      ref="horizontalThumbRef"
      :data="horizontalData"
      direction="horizontal"
      :both="isBoth"
      @scroll="handleHorizontalScroll"
    />
    <thumb
      v-if="!hide && hasVerticalScrollbar"
      ref="verticalThumbRef"
      :data="verticalData"
      direction="vertical"
      :both="isBoth"
      @scroll="handleVerticalScroll"
    />
  </div>
</template>
