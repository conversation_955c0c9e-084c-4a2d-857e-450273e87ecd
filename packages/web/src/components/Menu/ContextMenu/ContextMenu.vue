<script setup lang="ts">
import { ref, reactive, nextTick, onMounted, onUnmounted, provide } from 'vue'
import { getBuildCurrentClassPrefix } from '@utils'

import type { ContextMenuProps, ContextMenuEmits, TriggerEvent } from './interface'
import type { JSX } from "vue/jsx-runtime";

const props = withDefaults(defineProps<ContextMenuProps>(), {
  hideOnSelect: true,
  trigger: 'context-menu',
})

const emit = defineEmits<ContextMenuEmits>()

const buildClassPrefix = getBuildCurrentClassPrefix('context-menu')
const triggerRef = ref<HTMLElement>()
const menuRef = ref<HTMLElement>()
const visible = ref(false)
const menuStyle = reactive({
  left: '0px',
  top: '0px',
})

function bindTriggerEvent() {
  const triggerList: TriggerEvent[] = Array.isArray(props.trigger) ? props.trigger : [props.trigger];
  const events: JSX.IntrinsicElements['div'] = {};
  if (triggerList.includes('click')) {
    events.onClick = handleContextMenu;
  }

  if (triggerList.includes('context-menu')) {
    events.onContextmenu = handleContextMenu;
  }
  return events;
}

const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()

  // 设置菜单位置
  menuStyle.left = event.clientX + 'px'
  menuStyle.top = event.clientY + 'px'

  visible.value = true

  // 下一帧检查菜单位置是否超出屏幕
  nextTick(() => {
    adjustMenuPosition(event.clientX, event.clientY)
  })
}

const adjustMenuPosition = (x: number, y: number) => {
  if (!menuRef.value) return

  const menu = menuRef.value
  const menuRect = menu.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  let adjustedX = x
  let adjustedY = y

  // 如果菜单超出右边界，向左调整
  if (x + menuRect.width > windowWidth) {
    adjustedX = windowWidth - menuRect.width - 4
  }

  // 如果菜单超出下边界，向上调整
  if (y + menuRect.height > windowHeight) {
    adjustedY = windowHeight - menuRect.height - 4
  }

  // 确保不超出左边界和上边界
  adjustedX = Math.max(4, adjustedX)
  adjustedY = Math.max(4, adjustedY)

  menuStyle.left = adjustedX + 'px'
  menuStyle.top = adjustedY + 'px'
}

const handleMaskClick = () => {
  visible.value = false
}

const handleSelect = (value: any, event: Event) => {
  emit('select', value, event)
  if (props.hideOnSelect) {
    visible.value = false
  }
}

const handleClickOutside = (event: MouseEvent) => {
  if (!visible.value) return

  const target = event.target as HTMLElement
  const menu = menuRef.value
  const trigger = triggerRef.value

  // 完全忽略右键相关的所有事件
  if (event.button === 2 || event.which === 3) return

  // 检查是否点击在菜单或触发器内（子菜单现在在菜单内，会被自动包含）
  const isClickInsideMenu = menu && menu.contains(target)
  const isClickInsideTrigger = trigger && trigger.contains(target)

  if (!isClickInsideMenu && !isClickInsideTrigger) {
    visible.value = false
  }
}

const handleEscape = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && visible.value) {
    visible.value = false
  }
}

// 全局右键事件处理函数 - 完全阻止所有右键相关行为
const handleGlobalContextMenu = (event: MouseEvent) => {
  if (!visible.value) return

  // 无条件阻止所有右键事件的默认行为和传播
  event.preventDefault()
  event.stopPropagation()
  event.stopImmediatePropagation()

  // 永远不关闭菜单
}

// 提供给子组件使用的方法
const closeMenu = () => {
  visible.value = false
}

// 提供上下文给子组件
provide('handleSelect', handleSelect)
provide('closeMenu', closeMenu)

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('mousedown', handleClickOutside) // 添加mousedown监听
  document.addEventListener('keydown', handleEscape)
  document.addEventListener('contextmenu', handleGlobalContextMenu)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('mousedown', handleClickOutside) // 移除mousedown监听
  document.removeEventListener('keydown', handleEscape)
  document.removeEventListener('contextmenu', handleGlobalContextMenu)
})
</script>
<template>
  <div
    ref="triggerRef"
    :class="buildClassPrefix('trigger')"
    v-bind="{ ...bindTriggerEvent() }"
  >
    <slot />
  </div>

  <Teleport to="body">
    <Transition :name="buildClassPrefix('menu-transition')">
      <div
        v-if="visible"
        ref="menuRef"
        :class="buildClassPrefix('menu')"
        :style="menuStyle"
        @contextmenu.prevent
      >
        <slot name="content" />
      </div>
    </Transition>
  </Teleport>

  <!-- 遮罩层，点击关闭菜单 -->
  <Teleport to="body">
    <div
      v-if="visible"
      :class="buildClassPrefix('mask')"
      @click="handleMaskClick"
      @contextmenu.prevent="handleMaskClick"
    />
  </Teleport>
</template>
