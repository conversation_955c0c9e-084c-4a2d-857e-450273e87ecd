/**
 * 检查值是否为函数
 * @param value 要检查的值
 * @returns 是否为函数
 */
export function isFunction(value: any): value is Function {
  return typeof value === 'function';
}

/**
 * 检查值是否为 null
 * @param value 要检查的值
 * @returns 是否为 null
 */
export function isNull(value: any): value is null {
  return value === null;
}

/**
 * 检查值是否为 undefined
 * @param value 要检查的值
 * @returns 是否为 undefined
 */
export function isUndefined(value: any): value is undefined {
  return value === undefined;
}

/**
 * 检查值是否为数字
 * @param value 要检查的值
 * @returns 是否为数字
 */
export function isNumber(value: any): value is number {
  return typeof value === 'number' && !isNaN(value);
}

/**
 * 检查值是否为对象
 * @param value 要检查的值
 * @returns 是否为对象
 */
export function isObject(value: any): value is object {
  return value !== null && typeof value === 'object' && !Array.isArray(value);
}
