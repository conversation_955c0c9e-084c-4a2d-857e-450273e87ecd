import { defineConfig } from 'vite'
import { fileURLToPath, URL } from "node:url";
import { resolve } from 'node:path'

export default defineConfig({
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      formats: ['es'],
      fileName: (format) => `index.${format}.js`,
    },
    sourcemap: false,
    outDir: 'dist',
    rollupOptions: {
      external: ['vue', 'vue-router', '@vueuse/core'],
    }
  },
  plugins: [],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
})
