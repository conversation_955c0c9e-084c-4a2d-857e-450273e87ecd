@import "../../../../assets/less/variables.less";

.@{u-prefix}-split-panel {
  display: flex;
  width: 100%;
  height: 100%;

  &-horizontal {
    flex-direction: row;
  }

  &-vertical {
    flex-direction: column;
  }

  &-pane {
    overflow: hidden;

    &-first {
      // 第一个面板的样式由计算属性控制
    }

    &-second {
      flex: 1;
      min-width: 0;
      min-height: 0;
    }
  }
}

.@{u-prefix}-split-trigger {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--u-bg-color-2, #f0f0f0);
  //border: 1px solid var(--u-color-neutral-3, #e0e0e0);
  transition: background-color 0.2s;
  user-select: none;

  &:hover:not(&-disabled) {
    background: var(--u-bg-color-3-hover, #e8e8e8);
  }

  &-horizontal {
    width: 6px;
    cursor: col-resize;
    margin: 0 1px;

    .@{u-prefix}-split-trigger-icon {
      display: flex;
      flex-direction: column;
      gap: 2px;
    }

    .@{u-prefix}-split-trigger-line {
      width: 2px;
      height: 12px;
      background: var(--u-text-color-placeholder, #999);
      border-radius: 1px;
    }
  }

  &-vertical {
    height: 6px;
    cursor: row-resize;
    margin: 1px 0;

    .@{u-prefix}-split-trigger-icon {
      display: flex;
      flex-direction: row;
      gap: 2px;
    }

    .@{u-prefix}-split-trigger-line {
      width: 12px;
      height: 2px;
      background: var(--u-text-color-placeholder, #999);
      border-radius: 1px;
    }
  }

  &-disabled {
    cursor: not-allowed;
    opacity: 0.5;

    &:hover {
      background: var(--u-bg-color-2, #f0f0f0);
    }
  }
}
