import { ref, onUnmounted } from 'vue'

interface ObserverEntry {
  element: HTMLElement
  callback: (isIntersecting: boolean) => void
  rootMargin: string
}

class GlobalIntersectionObserverManager {
  private observers = new Map<string, IntersectionObserver>()
  private entries = new Map<HTMLElement, ObserverEntry>()

  observe(element: HTMLElement, callback: (isIntersecting: boolean) => void, options: { rootMargin?: string } = {}) {
    const rootMargin = options.rootMargin || '0px'
    const key = `rootMargin:${rootMargin}`

    // 获取或创建对应的 observer
    let observer = this.observers.get(key)
    if (!observer) {
      observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          const entryData = this.entries.get(entry.target as HTMLElement)
          if (entryData) {
            entryData.callback(entry.isIntersecting)
          }
        })
      }, {
        rootMargin,
        threshold: 0
      })
      this.observers.set(key, observer)
    }

    // 记录元素和回调
    const entryData: ObserverEntry = { element, callback, rootMargin }
    this.entries.set(element, entryData)

    // 开始观察
    observer.observe(element)

    // 返回清理函数
    return () => {
      this.unobserve(element)
    }
  }

  unobserve(element: HTMLElement) {
    const entryData = this.entries.get(element)
    if (!entryData) return

    // 从对应的 observer 中移除元素
    const key = `rootMargin:${entryData.rootMargin}`
    const observer = this.observers.get(key)
    if (observer) {
      observer.unobserve(element)
    }

    // 清理记录
    this.entries.delete(element)

    // 如果这个 observer 没有观察任何元素了，就销毁它
    const hasElementsForThisObserver = Array.from(this.entries.values())
      .some(entry => entry.rootMargin === entryData.rootMargin)

    if (!hasElementsForThisObserver && observer) {
      observer.disconnect()
      this.observers.delete(key)
    }
  }
}

// 全局单例
const globalObserverManager = new GlobalIntersectionObserverManager()

export function useGlobalIntersectionObserver(
  callback: (isIntersecting: boolean) => void,
  options: { rootMargin?: string } = {}
) {
  let cleanup: (() => void) | null = null
  let currentTarget: HTMLElement | null = null

  const start = (target: HTMLElement) => {
    if (target && !cleanup) {
      currentTarget = target
      cleanup = globalObserverManager.observe(target, callback, options)
    }
  }

  const stop = () => {
    if (cleanup) {
      cleanup()
      cleanup = null
      currentTarget = null
    }
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    stop()
  })

  return { start, stop }
}
