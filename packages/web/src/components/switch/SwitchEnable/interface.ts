import type { SwitchProps } from 'tdesign-vue-next'

/**
 * SwitchEnable 组件的 Props 类型
 * 继承 TDesign Switch 的所有 props，包括 value 用于 v-model
 */
export interface SwitchEnableProps extends SwitchProps {
  /**
   * 开关的值，支持 v-model:value
   */
  value?: boolean;
  /**
   * 自定义颜色
   */
  color?: 'green';
}

/**
 * SwitchEnable 组件的 Emits 类型
 */
export interface SwitchEnableEmits {
  /**
   * 开关状态变化时触发
   * @param value 新的开关状态
   * @param context 事件上下文
   */
  change: [value: boolean, context: { e: Event }];
}
