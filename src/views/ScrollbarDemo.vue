<script setup lang="ts">
import { ref } from 'vue';
import { Scrollbar } from '@xiaou66/u-web-ui';

const scrollbarRef = ref();

function handleScroll(ev: Event) {
  console.log('Scrollbar scrolled:', ev);
}

function scrollToTop() {
  scrollbarRef.value?.scrollTop(0);
}

function scrollToBottom() {
  scrollbarRef.value?.scrollTop(1000);
}

function scrollToLeft() {
  scrollbarRef.value?.scrollLeft(0);
}

function scrollToRight() {
  scrollbarRef.value?.scrollLeft(500);
}
</script>

<template>
  <div class="demo-container">
    <h1>Scrollbar 滚动条组件演示</h1>

    <div class="demo-section">
      <h2>基础用法 - 嵌入式滚动条</h2>
      <p>默认的嵌入式滚动条，鼠标悬停时显示</p>

      <div class="scrollbar-container">
        <Scrollbar
          ref="scrollbarRef"
          style="max-height: 200px;"
          @scroll="handleScroll"
        >
          <div class="content">
            <div v-for="i in 20" :key="i" class="content-item">
              <h4>内容项 {{ i }}</h4>
              <p>这是一个很长的内容项，用来测试横向滚动功能。Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.321312312321321321312312321321321312312321312312312321312332131231232132132131231232131231231232131233213123123213123123123213123</p>
            </div>
          </div>
        </Scrollbar>
      </div>

      <div class="controls">
        <button @click="scrollToTop">滚动到顶部</button>
        <button @click="scrollToBottom">滚动到底部</button>
        <button @click="scrollToLeft">滚动到左侧</button>
        <button @click="scrollToRight">滚动到右侧</button>
      </div>
    </div>

    <div class="demo-section">
      <h2>轨道式滚动条</h2>
      <p>显示滚动条轨道的样式</p>
      <div class="scrollbar-container">
        <Scrollbar
          type="track"
          style="height: 200px;"
          @scroll="handleScroll"
        >
          <div class="content">
            <div v-for="i in 15" :key="i" class="content-item">
              <h4>轨道式内容项 {{ i }}</h4>
              <p>这是轨道式滚动条的内容。可以看到滚动条轨道始终可见。</p>
            </div>
          </div>
        </Scrollbar>
      </div>
    </div>

    <div class="demo-section">
      <h2>禁用滚动方向</h2>
      <p>可以禁用横向或纵向滚动</p>

      <div class="scrollbar-container">
        <Scrollbar
          :disable-horizontal="true"
          style="height: 150px;"
          @scroll="handleScroll"
        >
          <div class="content">
            <div v-for="i in 10" :key="i" class="content-item">
              <h4>仅纵向滚动 {{ i }}</h4>
              <p>这个滚动条禁用了横向滚动，只能纵向滚动。即使内容很长也不会显示横向滚动条。321312312321321321312312321321321312312321312312312321312332131231232132132131231232131231231232131233213123123213123123123213123</p>
            </div>
          </div>
        </Scrollbar>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.demo-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;

  h2 {
    color: var(--u-text-color-primary);
    margin-bottom: 8px;
  }

  p {
    color: var(--u-text-color-secondary);
    margin-bottom: 16px;
  }
}

.scrollbar-container {
  border: 1px solid var(--u-color-neutral-3);
  border-radius: var(--u-radius-default);
  overflow: hidden;
  margin-bottom: 16px;
}

.scrollbar-container .content {
  padding: 16px;
  max-width: none !important; // 覆盖全局样式
  overflow-x: visible !important; // 覆盖全局样式
}

.content-item {
  margin-bottom: 16px;
  padding: 12px;
  background: var(--u-bg-color-2);
  border-radius: var(--u-radius-small);

  h4 {
    margin: 0 0 8px 0;
    color: var(--u-text-color-primary);
  }

  p {
    margin: 0;
    color: var(--u-text-color-secondary);
    line-height: 1.5;
  }
}

.controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;

  button {
    padding: 8px 16px;
    background: var(--u-bg-color-3);
    border: 1px solid var(--u-color-neutral-3);
    border-radius: var(--u-radius-small);
    color: var(--u-text-color-primary);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--u-bg-color-3-hover);
      color: var(--u-text-color-hover);
    }

    &:active {
      background: var(--u-bg-color-3-active);
    }
  }
}
</style>
