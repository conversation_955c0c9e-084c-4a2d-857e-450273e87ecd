import type { RouteRecord, RouteRecordRaw } from 'vue-router'



/**
 * LeftMenu 组件实例
 */
export interface LeftMenuInstance {
  /**
   * 切换菜单折叠状态
   */
  changeCollapsed: () => void;
  /**
   * 刷新菜单
   */
  refreshRouter: () => void;
}

/**
 * 菜单项
 */
export interface MenuItem {
  title: string;
  icon: string;
  menu?: boolean;
}
export type MenuRouterItem = ((RouteRecord | RouteRecordRaw) & { meta: MenuItem });
export type LoadRouterType = () => MenuRouterItem[];
/**
 * LeftMenu 组件 Props
 */
export interface LeftMenuProps {
  /**
   * 是否隐藏操作按钮
   */
  hideOperations?: boolean;
  /**
   * 自定义加载路由事件
   */
  loadRouter?: LoadRouterType;
  /**
   * 是否启用刷新事件监听器
   */
  refreshEventListener?: boolean;
  /**
   * 菜单大小
   */
  size?: 'small' | 'default'
}


export enum LeftMenuEvents {
  /**
   * 刷新菜单时间
   */
  Refresh = "u:leftMenu:refresh"
}

/**
 * 菜单事件派发
 */
export const LeftMenuEventDispatch = {
  refresh: () => {
    window.dispatchEvent(new CustomEvent(LeftMenuEvents.Refresh));
  }
}
