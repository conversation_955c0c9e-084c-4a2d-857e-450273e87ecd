<script setup lang="ts">
import { computed, ref, toRefs, watch, useSlots } from 'vue';
import { getBuildCurrentClassPrefix } from '@utils';
import { isFunction, isNull, isUndefined } from '@utils';
import type { SwitchPlusProps, SwitchPlusEmits, SwitchPlusSlots } from './interface';

// 定义组件选项
defineOptions({
  name: 'SwitchPlus',
});

// 定义 model
const value = defineModel<string | number | boolean>('value', {
  default: false,
});

// 定义 props (排除 value)
const props = withDefaults(defineProps<SwitchPlusProps>(), {
  defaultChecked: false,
  disabled: false,
  loading: false,
  type: 'circle',
  size: 'medium',
  checkedValue: true,
  uncheckedValue: false,
});

// 定义 emits (排除 update:value)
const emit = defineEmits<SwitchPlusEmits>();

// 定义插槽
defineSlots<SwitchPlusSlots>();

// 获取插槽
const slots = useSlots();

// 响应式引用
const { size } = toRefs(props);
const buildClassPrefix = getBuildCurrentClassPrefix('switch-plus');
const prefixCls = buildClassPrefix();

const mergedDisabled = computed(() => props.disabled);
const mergedSize = computed(() => props.size);

// 初始化 value 如果未设置
if (value.value === false && props.defaultChecked) {
  value.value = props.checkedValue;
}

const computedCheck = computed<boolean>(
  () => value.value === props.checkedValue
);
const _loading = ref(false);
const computedLoading = computed(() => _loading.value || props.loading);

const handleChange = (checked: boolean, ev: Event) => {
  const newValue = checked ? props.checkedValue : props.uncheckedValue;
  value.value = newValue;
  emit('change', newValue, ev);
};

const handleClick = async (ev: Event) => {
  if (computedLoading.value || mergedDisabled.value) {
    return;
  }
  const checked = !computedCheck.value;
  const checkedValue = checked ? props.checkedValue : props.uncheckedValue;
  const shouldChange = props.beforeChange;

  if (isFunction(shouldChange)) {
    _loading.value = true;
    try {
      const result = await shouldChange(checkedValue);
      if (result ?? true) {
        handleChange(checked, ev);
      }
    } finally {
      _loading.value = false;
    }
  } else {
    handleChange(checked, ev);
  }
};

const handleFocus = (ev: FocusEvent) => {
  emit('focus', ev);
};

const handleBlur = (ev: FocusEvent) => {
  emit('blur', ev);
};

// 监听 value 变化，处理 null/undefined 情况
watch(value, (newValue) => {
  if (isUndefined(newValue) || isNull(newValue)) {
    value.value = props.uncheckedValue;
  }
});

const cls = computed(() => [
  prefixCls,
  `${prefixCls}-type-${props.type}`,
  {
    [`${prefixCls}-medium`]: mergedSize.value === 'medium',
    [`${prefixCls}-small`]: mergedSize.value === 'small',
    [`${prefixCls}-mini`]: mergedSize.value === 'mini',
    [`${prefixCls}-checked`]: computedCheck.value,
    [`${prefixCls}-disabled`]: mergedDisabled.value,
    [`${prefixCls}-loading`]: computedLoading.value,
    [`${prefixCls}-custom-color`]:
      props.type === 'line' && (props.checkedColor || props.uncheckedColor),
  },
]);

const buttonStyle = computed(() => {
  if (computedCheck.value && props.checkedColor) {
    return props.type === 'line'
      ? { '--custom-color': props.checkedColor }
      : { backgroundColor: props.checkedColor };
  }
  if (!computedCheck.value && props.uncheckedColor) {
    return props.type === 'line'
      ? { '--custom-color': props.uncheckedColor }
      : { backgroundColor: props.uncheckedColor };
  }
  return undefined;
});

const handleStyle = computed(() => {
  // 如果是加载状态，不改变图标颜色
  if (computedLoading.value) {
    return undefined;
  }

  // 如果设置了自定义颜色，图标颜色跟随
  if (computedCheck.value && props.checkedColor) {
    return { color: props.checkedColor };
  }
  if (!computedCheck.value && props.uncheckedColor) {
    return { color: props.uncheckedColor };
  }

  return undefined;
});
</script>

<template>
  <button
    type="button"
    role="switch"
    :aria-checked="computedCheck"
    :class="cls"
    :style="buttonStyle"
    :disabled="mergedDisabled || computedLoading"
    @click="handleClick"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <span :class="`${prefixCls}-handle`" :style="handleStyle">
      <span :class="`${prefixCls}-handle-icon`">
        <span v-if="computedLoading" class="i-p-loading" :class="`${prefixCls}-loading-icon`" />
        <template v-else>
          <slot v-if="computedCheck" name="checked-icon" />
          <slot v-else name="unchecked-icon" />
        </template>
      </span>
    </span>
    <!--  prettier-ignore  -->
    <template
      v-if="
        type !== 'line' &&
        size !== 'small' &&
        (slots.checked || checkedText || slots.unchecked || uncheckedText)
      "
    >
      <span :class="`${prefixCls}-text-holder`">
        <slot v-if="computedCheck" name="checked">{{ checkedText }}</slot>
        <slot v-else name="unchecked">{{ uncheckedText }}</slot>
      </span>
      <span :class="`${prefixCls}-text`">
        <slot v-if="computedCheck" name="checked">{{ checkedText }}</slot>
        <slot v-else name="unchecked">{{ uncheckedText }}</slot>
      </span>
    </template>
  </button>
</template>
