<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useGlobalIntersectionObserver } from './useGlobalIntersectionObserver'

const props = withDefaults(defineProps<{
  w?: string;
  h?: string;
  distance?: number;
}>(), {
  w: '100%',
  h: '100%',
  distance: 0,
});

const load = ref<boolean>(false);
const box = ref<HTMLElement>();

const { start, stop } = useGlobalIntersectionObserver(
  (isIntersecting) => {
    if (isIntersecting) { // 当内容可见
      load.value = true;
      stop(); // 停止观察
    }
  },
  {
    rootMargin: `${props.distance}px`
  }
);

// 当 box 元素准备好时开始观察
watch(box, (newBox) => {
  if (newBox) {
    start(newBox);
  }
}, { immediate: true });
</script>

<template>
  <slot v-if="load" name="default"></slot>
  <div v-else ref="box"
        :style="{ height: h, width: w }">
  </div>
</template>

