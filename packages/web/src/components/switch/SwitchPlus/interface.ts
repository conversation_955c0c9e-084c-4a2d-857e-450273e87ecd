import type { PropType } from 'vue';

/**
 * SwitchPlus 组件的 Props 类型
 */
export interface SwitchPlusProps {
  /**
   * @zh 绑定值
   * @en Value
   */
  modelValue?: string | number | boolean;
  /**
   * @zh 默认选中状态（非受控状态）
   * @en Default selected state (uncontrolled state)
   */
  defaultChecked?: boolean;
  /**
   * @zh 是否禁用
   * @en Whether to disable
   */
  disabled?: boolean;
  /**
   * @zh 是否为加载中状态
   * @en Whether it is loading state
   */
  loading?: boolean;
  /**
   * @zh 开关的类型
   * @en Type of switch
   * @values 'circle', 'round', 'line'
   */
  type?: 'circle' | 'round' | 'line';
  /**
   * @zh 开关的大小
   * @en Size of switch
   * @defaultValue 'medium'
   */
  size?: 'mini' | 'small' | 'medium' | 'large';
  /**
   * @zh 选中时的值
   * @en Value when checked
   */
  checkedValue?: string | number | boolean;
  /**
   * @zh 未选中时的值
   * @en Value when unchecked
   */
  uncheckedValue?: string | number | boolean;
  /**
   * @zh 选中时的开关颜色
   * @en The color of the switch when checked
   */
  checkedColor?: string;
  /**
   * @zh 未选中时的开关颜色
   * @en The color of the switch when unchecked
   */
  uncheckedColor?: string;
  /**
   * @zh switch 状态改变前的钩子， 返回 false 或者返回 Promise 且被 reject 则停止切换。
   * @en before-change hook before the switch state changes. If false is returned or a Promise is returned and then is rejected, will stop switching
   */
  beforeChange?: (
    newValue: string | number | boolean
  ) => Promise<boolean | void> | boolean | void;
  /**
   * @zh 打开状态时的文案（`type='line'`和`size='small'`时不生效）
   * @en Copywriting when opened (not effective when `type='line'` and `size='small'`)
   */
  checkedText?: string;
  /**
   * @zh 关闭状态时的文案（`type='line'`和`size='small'`时不生效）
   * @en Copywriting when closed (not effective when `type='line'` and `size='small'`)
   */
  uncheckedText?: string;
}

/**
 * SwitchPlus 组件的 Emits 类型
 */
export interface SwitchPlusEmits {
  'update:modelValue': [value: boolean | string | number];
  /**
   * @zh 值改变时触发
   * @en Trigger when the value changes
   * @param { boolean | string | number } value
   * @param {Event} ev
   */
  'change': [value: boolean | string | number, ev: Event];
  /**
   * @zh 组件获得焦点时触发
   * @en Triggered when the component gets focus
   * @property {FocusEvent} ev
   */
  'focus': [ev: FocusEvent];
  /**
   * @zh 组件失去焦点时触发
   * @en Fired when the component loses focus
   * @property {FocusEvent} ev
   */
  'blur': [ev: FocusEvent];
}

/**
 * SwitchPlus 组件的插槽类型
 */
export interface SwitchPlusSlots {
  /**
   * @zh 打开状态时的文案（`type='line'`和`size='small'`时不生效）
   * @en Copywriting when opened (not effective when `type='line'` and `size='small'`)
   */
  checked?(): any;
  /**
   * @zh 关闭状态时的文案（`type='line'`和`size='small'`时不生效）
   * @en Copywriting when closed (not effective when `type='line'` and `size='small'`)
   */
  unchecked?(): any;
  /**
   * @zh 打开状态时，按钮上的图标
   * @en The icon on the button when opened
   */
  'checked-icon'?(): any;
  /**
   * @zh 关闭状态时，按钮上的图标
   * @en The icon on the button when closed
   */
  'unchecked-icon'?(): any;
}
