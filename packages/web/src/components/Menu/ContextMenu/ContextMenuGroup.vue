<script setup lang="ts">
import { getBuildCurrentClassPrefix } from '@utils'

import type { ContextMenuGroupProps } from './interface'

defineProps<ContextMenuGroupProps>()

const buildClassPrefix = getBuildCurrentClassPrefix('context-menu', 'group')
</script>
<template>
  <div :class="buildClassPrefix()">
    <div v-if="title" :class="buildClassPrefix('title')">{{ title }}</div>
    <slot />
  </div>
</template>
