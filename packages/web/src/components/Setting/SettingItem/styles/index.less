@import "../../../../assets/less/variables.less";

@setting-item: @{u-prefix}-setting-item;

.@{setting-item} {
  background: var(--u-bg-color-3);
  padding: 15px 10px;
  transition: all 280ms linear;
  user-select: none;
  &:hover {
    background: rgb(var(--gray-2), 0.5);
  }
}
.@{setting-item}-small {
  padding: 8px;
  .@{setting-item}-title {
    font-size: 14px;
  }
}

.@{setting-item}-title {
  --at-apply: text-4 font-bold mb-1;
}
.@{setting-item}-desc {
  font-size: 12px;
  color: var(--u-text-color-tips);
}
