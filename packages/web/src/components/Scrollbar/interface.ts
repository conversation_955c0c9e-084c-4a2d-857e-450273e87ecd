import type { StyleValue } from 'vue';

export interface ThumbData {
  ratio: number;
  thumbSize: number;
  max: number;
}

export interface ThumbMap {
  size: 'width' | 'height';
  direction: 'left' | 'top';
  offset: 'offsetWidth' | 'offsetHeight';
  client: 'clientX' | 'clientY';
}

export interface ScrollbarProps {
  /**
   * @zh 类型
   * @en Type
   */
  type?: 'track' | 'embed';
  /**
   * @zh 外层的类名
   * @en Outer class
   */
  outerClass?: any;
  /**
   * @zh 外层的样式
   * @en Outer style
   */
  outerStyle?: StyleValue;
  /**
   * @zh 隐藏滚动条
   * @en Hide scrollbar
   */
  hide?: boolean;
  /**
   * @zh 禁用横向滚动
   * @en Disable horizontal scroll
   */
  disableHorizontal?: boolean;
  /**
   * @zh 禁用纵向滚动
   * @en Disable vertical scroll
   */
  disableVertical?: boolean;
}

export interface ScrollbarEmits {
  /**
   * @zh 滚动时触发
   * @en Triggered when scroll
   */
  (e: 'scroll', ev: Event): void;
}

export interface ThumbProps {
  data?: ThumbData;
  direction?: 'horizontal' | 'vertical';
  alwaysShow?: boolean;
  both?: boolean;
}

export interface ThumbEmits {
  (e: 'scroll', offset: number): void;
}

export interface ScrollbarComponentProps {
  /**
   * @zh 类型
   * @en Type
   */
  type?: 'track' | 'embed';
  /**
   * @zh 外层的类名
   * @en Outer class
   */
  outerClass?: any;
  /**
   * @zh 外层的样式
   * @en Outer style
   */
  outerStyle?: StyleValue;
  /**
   * @zh 隐藏滚动条
   * @en Hide scrollbar
   */
  hide?: boolean;
  /**
   * @zh 禁用横向滚动
   * @en Disable horizontal scroll
   */
  disableHorizontal?: boolean;
  /**
   * @zh 禁用纵向滚动
   * @en Disable vertical scroll
   */
  disableVertical?: boolean;
}

export interface ScrollbarComponentEmits {
  /**
   * @zh 滚动时触发
   * @en Triggered when scroll
   */
  (e: 'scroll', ev: Event): void;
}

export interface ScrollbarInstance {
  /**
   * @zh 滚动
   * @en scrollTo
   * @public
   * @param {number | {left?: number;top?: number}} options
   * @param {number} y
   */
  scrollTo: (options?: | number | { left?: number; top?: number; }, y?: number) => void;
  /**
   * @zh 纵向滚动
   * @en scroll vertically
   * @public
   * @param {number} top
   */
  scrollTop: (top: number) => void;

  /**
   * @zh 横向滚动
   * @en scroll horizontal
   * @public
   * @param {number} left
   */
  scrollLeft: (left: number) => void;
}
