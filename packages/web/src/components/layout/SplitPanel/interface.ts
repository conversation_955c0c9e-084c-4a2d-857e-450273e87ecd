import type { JSX } from "vue/jsx-runtime";

export interface SplitPanelProps {
  /**
   * @zh 分割框的 html 标签
   * @en The html tag of the split box
   */
  component?: string;
  /**
   * @zh 分割的方向
   * @en Direction of division
   */
  direction?: 'horizontal' | 'vertical';
  /**
   * @zh 分割的大小，可以是 0~1 代表百分比，或具体数值的像素，如 300px
   * @en The size of the segmentation, it can be 0~1 representing a percentage, or a specific number of pixels, such as 300px
   */
  size?: number | string;
  /**
   * @zh 默认分割的大小，可以是 0~1 代表百分比，或具体数值的像素，如 300px
   * @en Default split size, it can be 0~1 representing a percentage, or a specific number of pixels, such as 300px
   */
  defaultSize?: number | string;
  /**
   * @zh 最小阈值，可以是 0~1 代表百分比，或具体数值的像素，如 300px
   * @en Minimum threshold, it can be 0~1 representing a percentage, or a specific number of pixels, such as 300px
   */
  min?: number | string;
  /**
   * @zh 最大阈值，可以是 0~1 代表百分比，或具体数值的像素，如 300px
   * @en Maximum threshold, it can be 0~1 representing a percentage, or a specific number of pixels, such as 300px
   */
  max?: number | string;
  /**
   * @zh 是否禁用
   * @en Whether to disable
   */
  disabled?: boolean;
}

export interface SplitPanelEmits {
  /**
   * @zh 开始拖拽之前触发
   * @en Triggered before dragging
   */
  (e: 'moveStart', ev: MouseEvent): void;
  /**
   * @zh 拖拽时触发
   * @en Triggered when dragging
   */
  (e: 'moving', ev: MouseEvent): void;
  /**
   * @zh 拖拽结束之后触发
   * @en Triggered after dragging ends
   */
  (e: 'moveEnd', ev: MouseEvent): void;
  /**
   * @zh 尺寸变化时触发
   * @en Triggered when size changes
   */
  (e: 'update:size', size: number | string): void;
}

export interface SplitPanelSlots {
  /**
   * @zh 第一个面板的内容
   * @en The contents of the first panel
   */
  first?(): JSX.Element;
  /**
   * @zh 第二个面板的内容
   * @en The contents of the second panel
   */
  second?(): JSX.Element;
  /**
   * @zh 伸缩杆的内容
   * @en The contents of the resize trigger
   */
  'resize-trigger'?(): JSX.Element;
  /**
   * @zh 伸缩杆的图标
   * @en Resize trigger icon
   */
  'resize-trigger-icon'?(): JSX.Element;
}
